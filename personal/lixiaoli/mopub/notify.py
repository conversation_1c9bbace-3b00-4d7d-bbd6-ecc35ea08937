from __future__ import annotations

import argparse
import asyncio
import re
import os


from caas.commands import BashScript, Exec, UploadFile
from caas.protocol import NetworkMode, VolumeMount
from caas_tool.caas_container import CaasContainer

TIMEOUT = 180
_SNAPSHOT_RE = re.compile(r"(az://orngtransfer/devaultoutbound/models/[^/\s]+/[^\s]+)")
ALIAS = os.getenv("OPENAI_USER")


# log_tail example:2025-07-23 20:09:08,404 - INFO - Your output is generated at: az://orngtransfer/devaultoutbound/models/lixiaoli/swe-vsc-m15-s120-0723
def parse(log_tail: str | None) -> str:
    if not log_tail:
        return ""
    match = _SNAPSHOT_RE.search(log_tail)
    return match.group(1) if match else ""


async def main(log_tail: str | None):
    snapshot_path = parse(log_tail)

    container = await CaasContainer.new(
        image_name="aio",
        caas_endpoint="https://southcentralus.caas.azure.com",
        idle_ttl=1200,
        memory_limit="4g",
        network=NetworkMode.CAAS_PUBLIC_ONLY,
    )
    print("CaaS container ready ✅")
    session = container.terminal_session.session

    # Upload mopub to the container
    print("Uploading mopub...")
    await session.run(
        Exec(
            ["bash", "-lc", "mkdir -p /usr/local/bin/mopub"],
            timeout=TIMEOUT,
            workdir="/",
            env=None,
        )
    )

    with open("/root/code/glass/personal/lixiaoli/mopub/webhook.py", "rb") as f:
        content = f.read()
    await session.run(UploadFile("/usr/local/bin/mopub/webhook.py", content))
    print("mopub uploaded ✅")

    print(f"Parsed snapshot_path: {snapshot_path}")


    output = await session.run(
        Exec(["bash", "-c", f"set -x && python --version"], workdir="/")
    )
    print(f"python version output: {output.decode().strip()}\n")


    output = await session.run(
        Exec(["bash", "-c", f"pip install requests"], workdir="/usr/local/bin/mopub")
    )
    print(f"pip install output: {output.decode().strip()}\n")

    output = await session.run(
        Exec(
            ["bash", "-c", f"python webhook.py {snapshot_path} --log_tail {log_tail} --openai_user {ALIAS}"],
            timeout=TIMEOUT,
            workdir="/usr/local/bin/mopub",
        )
    )
    print(f"webhook output: {output.decode().strip()}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="last line of prepare_snapshot.log")
    parser.add_argument("log_tail", nargs="?", default=None, help="Log tail (optional)")
    args = parser.parse_args()
    asyncio.run(main(args.log_tail))
