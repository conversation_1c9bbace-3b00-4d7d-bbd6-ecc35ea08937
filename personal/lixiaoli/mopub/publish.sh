#!/usr/bin/env bash
set -euo pipefail

############################################################
# Required environment variables (can be exported outside script or changed to read parameters):
#   ORG          Organization name, e.g. "contoso"
#   PROJECT      Project name, e.g. "myproject"
#   REPO         Repository name or GUID
#   FILE         Local JSON file path to include in PR, e.g. "./generated/settings.json"
# Optional:
#   BASE_BRANCH  Target branch, defaults to "main"
#   NEW_BRANCH   New branch name, defaults to "add-$(basename $FILE)-$(date +%s)"
############################################################
ORG="project-argos"
PROJECT="Mumford"
REPO="oai-engine-configs"
FILE="/Users/<USER>/Projects/oai-engine-configs/models/ledgers/swe-vsc-m15-s200-0728.json"

BASE_BRANCH=${BASE_BRANCH:-main}
NEW_BRANCH=${NEW_BRANCH:-add-$(basename "$FILE" .json)-$(date +%s)}

############################################################
# Step 0: Get Azure DevOps Bearer Token (valid for 1 hour)
# Note: 499b84ac-1321-427f-aa17-267ca6975798 is the Azure DevOps
#       resource ID in Entra ID.
############################################################
TOKEN=$(az account get-access-token \
          --resource 499b84ac-1321-427f-aa17-267ca6975798 \
          --query accessToken -o tsv)

API="https://dev.azure.com/${ORG}/${PROJECT}/_apis"

############################################################
# Step 1: Get current HEAD commit ID of target branch
############################################################
HEAD=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  "${API}/git/repositories/${REPO}/refs/heads/${BASE_BRANCH}?api-version=7.1" |
  jq -r '.value[0].objectId')

echo "Base branch HEAD: $HEAD"

# Check if HEAD commit ID was successfully retrieved
if [ "$HEAD" = "null" ] || [ -z "$HEAD" ]; then
  echo "❌ Error: Unable to get HEAD commit ID for branch '${BASE_BRANCH}'"
  echo "Please check if the branch name is correct or if the branch exists"
  exit 1
fi

############################################################
# Step 2: Create new branch and commit JSON file in one push
############################################################
FILE_PATH="/models/ledgers/$(basename "$FILE")"
FILE_CONTENT=$(jq -Rs . < "$FILE")     # Convert to JSON string

PUSH_RESULT=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pushes?api-version=7.1" \
  -d @- <<EOF
{
  "refUpdates": [
    { "name": "refs/heads/${NEW_BRANCH}", "oldObjectId": "${HEAD}" }
  ],
  "commits": [
    {
      "comment": "Add $(basename "$FILE") ledger",
      "changes": [
        {
          "changeType": "add",
          "item": { "path": "${FILE_PATH}" },
          "newContent": {
            "content": ${FILE_CONTENT},
            "contentType": "rawtext"
          }
        }
      ]
    }
  ]
}
EOF
)

# Check if push operation was successful
if echo "$PUSH_RESULT" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ Push operation failed:"
  echo "$PUSH_RESULT" | jq -r '.message'
  exit 1
fi

echo "✅ Pushed ${FILE_PATH} to ${NEW_BRANCH}"

############################################################
# Step 3: Create Pull Request
############################################################
PR_JSON=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pullrequests?api-version=7.1" \
  -d @- <<EOF
{
  "sourceRefName": "refs/heads/${NEW_BRANCH}",
  "targetRefName": "refs/heads/${BASE_BRANCH}",
  "title": "Add $(basename "$FILE")",
  "description": "Auto-generated via script",
  "completionOptions": {
    "deleteSourceBranch": true
  }
}
EOF)

# Check if PR creation was successful
if echo "$PR_JSON" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ Failed to create Pull Request:"
  echo "$PR_JSON" | jq -r '.message'
  exit 1
fi

PR_ID=$(echo "$PR_JSON" | jq -r '.pullRequestId')

# Check if PR information was successfully retrieved
if [ "$PR_ID" = "null" ] || [ -z "$PR_ID" ]; then
  echo "❌ Unable to get Pull Request ID, creation may have failed"
  echo "API response:"
  echo "$PR_JSON" | jq .
  exit 1
fi

echo "✅ Pull Request #${PR_ID} created successfully!"
echo "🔗 https://dev.azure.com/${ORG}/${PROJECT}/_git/${REPO}/pullrequest/${PR_ID}"